// uniCloud云函数：更新用户信息
'use strict';

/**
 * 更新用户昵称和头像信息
 * 
 * @param {Object} event 
 * @param {string} event.openid - 用户openid
 * @param {string} event.nickname - 用户昵称
 * @param {string} event.avatar - 用户头像URL
 * @param {string} event.deviceId - 设备唯一标识
 * @returns {Object} 更新结果
 */
exports.main = async (event, context) => {
  try {
    const { openid, nickname, avatar, deviceId } = event
    const { CLIENTUA, CLIENTIP } = context

    // 参数验证
    if (!openid) {
      return {
        errCode: 'INVALID_PARAM',
        errMsg: 'openid参数不能为空'
      }
    }

    if (!nickname && !avatar) {
      return {
        errCode: 'INVALID_PARAM',
        errMsg: '昵称和头像至少需要提供一个'
      }
    }

    // 获取数据库引用
    const db = uniCloud.database()
    const userCollection = db.collection('users')
    
    // 构建更新数据
    const updateData = {
      updateTime: new Date(),
      lastActiveTime: new Date(),
      lastActiveIP: CLIENTIP,
      lastActiveUA: CLIENTUA
    }

    if (nickname) {
      updateData.nickname = nickname
    }

    if (avatar) {
      updateData.avatar = avatar
    }

    if (deviceId) {
      updateData.deviceId = deviceId
    }

    // 更新用户信息
    const result = await userCollection.where({
      openid: openid
    }).update(updateData)

    if (result.updated === 0) {
      return {
        errCode: 'USER_NOT_FOUND',
        errMsg: '用户不存在'
      }
    }

    console.log('用户信息更新成功:', { openid, updated: result.updated })

    return {
      errCode: 0,
      errMsg: '更新成功',
      data: {
        updated: result.updated,
        updateData: updateData
      }
    }

  } catch (error) {
    console.error('更新用户信息失败:', error)
    return {
      errCode: 'UPDATE_USER_FAILED',
      errMsg: '更新用户信息失败: ' + error.message
    }
  }
}
