// uniCloud云函数：获取任务详情
'use strict';

/**
 * 获取视频处理任务详情
 */
exports.main = async (event, context) => {
  try {
    const { taskId } = event
    
    if (!taskId) {
      return {
        errCode: 'INVALID_PARAM',
        errMsg: 'taskId参数不能为空'
      }
    }
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    // 查询任务详情
    const result = await db.collection('tasks')
      .doc(taskId)
      .get()
    
    if (!result.data || result.data.length === 0) {
      return {
        errCode: 'TASK_NOT_FOUND',
        errMsg: '未找到指定任务'
      }
    }
    
    const taskDetail = result.data[0] || result.data
    
    return {
      errCode: 0,
      errMsg: '查询成功',
      taskDetail
    }
    
  } catch (error) {
    console.error('查询任务详情失败:', error)
    return {
      errCode: 'QUERY_TASK_DETAIL_FAILED',
      errMsg: '查询任务详情失败: ' + error.message
    }
  }
}
