<template>
  <view class="history-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">历史记录</text>
      <text class="page-subtitle">查看您的视频处理历史</text>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-filter-section">
      <!-- 搜索栏 -->
      <view class="search-wrapper">
        <view class="search-input-wrapper">
          <text class="search-icon">🔍</text>
          <input
            v-model="searchKeyword"
            placeholder="搜索文件名或关键词..."
            class="search-input"
            @input="onSearchInput"
          />
          <text v-if="searchKeyword" @click="clearSearch" class="clear-icon">✕</text>
        </view>
      </view>

      <!-- 筛选选项 -->
      <view class="filter-tabs">
        <view
          v-for="filter in filterOptions"
          :key="filter.value"
          class="filter-tab"
          :class="{ active: currentFilter === filter.value }"
          @click="changeFilter(filter.value)"
        >
          <text class="filter-text">{{ filter.label }}</text>
          <text v-if="filter.count > 0" class="filter-count">{{ filter.count }}</text>
        </view>
      </view>
    </view>
    
    <!-- 历史记录内容 -->
    <view class="history-content">
      <!-- 统计信息 -->
      <view v-if="historyList.length > 0" class="stats-card card">
        <view class="card-body">
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{ historyList.length }}</text>
              <text class="stat-label">总记录</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ getCompletedCount() }}</text>
              <text class="stat-label">已完成</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ getProcessingCount() }}</text>
              <text class="stat-label">处理中</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ getFailedCount() }}</text>
              <text class="stat-label">失败</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 历史记录列表 -->
      <scroll-view class="history-list" scroll-y @scrolltolower="loadMore">
        <view v-if="filteredList.length === 0" class="empty-state">
          <view class="empty-content">
            <text class="empty-icon">📝</text>
            <text class="empty-text">{{ getEmptyText() }}</text>
            <text class="empty-desc">{{ getEmptyDesc() }}</text>
          </view>
        </view>

        <view v-else class="history-items">
          <view
            v-for="item in filteredList"
            :key="item._id"
            class="history-item card"
            @click="viewDetail(item)"
          >
            <view class="card-body">
              <view class="item-header">
                <view class="item-status">
                  <view class="tag" :class="getStatusTagClass(item.status)">
                    <text>{{ getStatusText(item.status) }}</text>
                  </view>
                </view>
                <text class="create-time">{{ formatTime(item.createTime) }}</text>
              </view>

              <view class="item-content">
                <view class="video-info">
                  <text class="file-name text-ellipsis">{{ item.fileName || '未命名视频' }}</text>
                  <view class="file-meta">
                    <text class="file-size">{{ formatFileSize(item.fileSize) }}</text>
                    <text v-if="item.duration" class="file-duration">{{ formatDuration(item.duration) }}</text>
                  </view>
                </view>

                <view v-if="item.status === 'completed'" class="process-info">
                  <text class="process-time">处理耗时: {{ getProcessDuration(item) }}</text>
                </view>

                <view v-if="item.status === 'failed'" class="error-info">
                  <text class="error-text">{{ item.errorMsg || '处理失败，请重试' }}</text>
                </view>
              </view>

              <view class="item-actions">
                <button
                  v-if="item.status === 'completed'"
                  @click.stop="viewResult(item)"
                  class="btn btn-primary btn-sm"
                >
                  <text class="btn-icon">👀</text>
                  <text>查看结果</text>
                </button>
                <button
                  v-if="item.status === 'failed'"
                  @click.stop="retryProcess(item)"
                  class="btn btn-primary btn-sm"
                >
                  <text class="btn-icon">🔄</text>
                  <text>重新处理</text>
                </button>
                <button
                  v-if="item.status === 'processing'"
                  @click.stop="viewProgress(item)"
                  class="btn btn-outline btn-sm"
                >
                  <text class="btn-icon">⏳</text>
                  <text>查看进度</text>
                </button>
                <button
                  @click.stop="deleteItem(item)"
                  class="btn btn-ghost btn-sm delete-btn"
                >
                  <text class="btn-icon">🗑️</text>
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载更多...</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getHistoryList, retryTask, deleteTask } from '@/utils/api'

// 响应式数据
const historyList = ref<any[]>([])
const searchKeyword = ref('')
const currentFilter = ref('all')
const hasMore = ref(true)
const loading = ref(false)

// 筛选选项
const filterOptions = computed(() => [
  { label: '全部', value: 'all', count: historyList.value.length },
  { label: '处理中', value: 'processing', count: getProcessingCount() },
  { label: '已完成', value: 'completed', count: getCompletedCount() },
  { label: '失败', value: 'failed', count: getFailedCount() }
])

// 计算属性：过滤后的列表
const filteredList = computed(() => {
  let list = historyList.value
  
  // 按状态筛选
  if (currentFilter.value !== 'all') {
    list = list.filter(item => item.status === currentFilter.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    list = list.filter(item => 
      (item.fileName || '').toLowerCase().includes(keyword) ||
      (item.errorMsg || '').toLowerCase().includes(keyword)
    )
  }
  
  return list
})

// 页面加载时获取历史记录
onMounted(() => {
  loadHistoryList()
})

// 加载历史记录列表
const loadHistoryList = async (isLoadMore = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    const newList = await getHistoryList(
      isLoadMore ? historyList.value.length : 0,
      20
    )
    
    if (isLoadMore) {
      historyList.value = [...historyList.value, ...newList]
    } else {
      historyList.value = newList
    }
    
    hasMore.value = newList.length === 20
    
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 统计方法
const getCompletedCount = (): number => {
  return historyList.value.filter(item => item.status === 'completed').length
}

const getProcessingCount = (): number => {
  return historyList.value.filter(item => item.status === 'processing').length
}

const getFailedCount = (): number => {
  return historyList.value.filter(item => item.status === 'failed').length
}

// 获取空状态文本
const getEmptyText = (): string => {
  if (searchKeyword.value) return '未找到相关记录'
  if (currentFilter.value === 'processing') return '暂无处理中的任务'
  if (currentFilter.value === 'completed') return '暂无已完成的任务'
  if (currentFilter.value === 'failed') return '暂无失败的任务'
  return '暂无历史记录'
}

const getEmptyDesc = (): string => {
  if (searchKeyword.value) return '尝试使用其他关键词搜索'
  if (currentFilter.value !== 'all') return '切换到其他筛选条件查看'
  return '上传视频后会在这里显示处理记录'
}

// 获取状态标签样式类
const getStatusTagClass = (status: string): string => {
  switch (status) {
    case 'processing': return 'tag-primary'
    case 'completed': return 'tag-success'
    case 'failed': return 'tag-error'
    default: return 'tag-primary'
  }
}

// 搜索输入
const onSearchInput = () => {
  // 可以添加防抖逻辑
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 切换筛选条件
const changeFilter = (filter: string) => {
  currentFilter.value = filter
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadHistoryList(true)
  }
}

// 查看详情
const viewDetail = (item: any) => {
  if (item.status === 'processing') {
    uni.navigateTo({
      url: `/pages/process/process?fileId=${item.originalVideoFileId}`
    })
  } else if (item.status === 'completed') {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${item._id}`
    })
  }
}

// 查看结果
const viewResult = (item: any) => {
  uni.navigateTo({
    url: `/pages/result/result?taskId=${item._id}`
  })
}

// 查看进度
const viewProgress = (item: any) => {
  uni.navigateTo({
    url: `/pages/process/process?taskId=${item._id}`
  })
}

// 重新处理
const retryProcess = (item: any) => {
  uni.showModal({
    title: '确认重新处理',
    content: '是否重新处理该视频？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await retryTask(item._id)
          
          uni.showToast({
            title: '已重新开始处理',
            icon: 'success'
          })
          
          // 刷新列表
          loadHistoryList()
          
        } catch (error) {
          console.error('重新处理失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 删除记录
const deleteItem = (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，是否确认删除？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteTask(item._id)
          
          // 从列表中移除
          const index = historyList.value.findIndex(i => i._id === item._id)
          if (index > -1) {
            historyList.value.splice(index, 1)
          }
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return ''
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}

// 格式化时长
const formatDuration = (duration: number): string => {
  if (!duration) return ''
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 获取处理耗时
const getProcessDuration = (item: any): string => {
  if (!item.createTime || !item.finishTime) return ''
  const start = new Date(item.createTime).getTime()
  const end = new Date(item.finishTime).getTime()
  const duration = Math.floor((end - start) / 1000)

  if (duration < 60) return `${duration}秒`
  if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`
  return `${Math.floor(duration / 3600)}小时${Math.floor((duration % 3600) / 60)}分钟`
  
  if (duration < 60) return `${duration}秒`
  return `${Math.floor(duration / 60)}分${duration % 60}秒`
}
</script>

<style scoped>
.history-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding-bottom: 120rpx; /* 为tabbar留出空间 */
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 48rpx 32rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
}

/* 搜索和筛选区域 */
.search-filter-section {
  padding: 32rpx;
  background: white;
  border-bottom: 2rpx solid #f3f4f6;
}

.search-wrapper {
  margin-bottom: 32rpx;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 24rpx;
  padding: 0 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.search-icon {
  font-size: 28rpx;
  color: #9ca3af;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #1f2937;
  background: transparent;
  border: none;
}

.clear-icon {
  font-size: 24rpx;
  color: #9ca3af;
  padding: 8rpx;
  cursor: pointer;

}

.clear-icon:active {
  color: #6b7280;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
  overflow-x: auto;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background-color: #f3f4f6;
  font-size: 26rpx;
  color: #6b7280;
  white-space: nowrap;
  transition: all 0.3s ease;
  cursor: pointer;

}

.filter-tab:active {
  background-color: #e5e7eb;
}

.filter-tab.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
}

.filter-text {
  font-weight: 500;
}

.filter-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  min-width: 32rpx;
  text-align: center;
}

.filter-tab.active .filter-count {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 历史记录内容 */
.history-content {
  padding: 32rpx;
}

/* 统计卡片 */
.stats-card {
  margin-bottom: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

/* 历史记录列表 */
.history-list {
  height: 600rpx;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-content {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* 历史记录项 */
.history-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  transition: all 0.3s ease;

}

.history-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-status {
  display: flex;
  align-items: center;
}

.create-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.item-content {
  margin-bottom: 24rpx;
}

.video-info {
  margin-bottom: 12rpx;
}

.file-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.file-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.file-size, .file-duration {
  font-size: 24rpx;
  color: #6b7280;
}

.process-info {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 500;
}

.error-info {
  font-size: 24rpx;
  color: #ef4444;
  line-height: 1.5;
}

.item-actions {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.delete-btn {
  margin-left: auto;
  width: 48rpx;
  height: 48rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

}

.delete-btn:active {
  background-color: #fee2e2;
  color: #dc2626;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 48rpx;
  color: #9ca3af;
  font-size: 26rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #e5e7eb;
  border-top: 3rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-weight: 500;
}
</style>
