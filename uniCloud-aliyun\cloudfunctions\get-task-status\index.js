// uniCloud云函数：获取任务状态
'use strict';

/**
 * 获取视频处理任务状态
 */
exports.main = async (event, context) => {
  try {
    const { fileId } = event
    
    if (!fileId) {
      return {
        errCode: 'INVALID_PARAM',
        errMsg: 'fileId参数不能为空'
      }
    }
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    // 查询任务状态
    const result = await db.collection('tasks')
      .where({
        originalVideoFileId: fileId
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()
    
    if (result.data.length === 0) {
      return {
        errCode: 'TASK_NOT_FOUND',
        errMsg: '未找到相关任务'
      }
    }
    
    const taskInfo = result.data[0]
    
    return {
      errCode: 0,
      errMsg: '查询成功',
      taskInfo
    }
    
  } catch (error) {
    console.error('查询任务状态失败:', error)
    return {
      errCode: 'QUERY_TASK_FAILED',
      errMsg: '查询任务状态失败: ' + error.message
    }
  }
}
