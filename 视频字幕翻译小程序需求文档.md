
## **微信小程序“视语翻译”技术方案 (VOD 核心版)**

### 1. 方案概述

本方案是对原技术选型的升级，将**腾讯云点播 VOD**作为视频处理的核心。它取代了原方案中分散的云存储、媒体处理MPS和微信同声传译插件，形成了一个统一、高效、自动化的视频处理流水线。

### 2. 修订后的技术架构

#### 2.1. 核心组件

| 类别 | 技术/服务 | 核心职责 |
| :--- | :--- | :--- |
| **前端框架** | **uni-app** | UI 界面、用户交互、调用云函数、使用 VOD SDK 上传视频。 |
| **后端服务** | **微信小程序云开发** | **业务逻辑粘合剂**。提供云函数和云数据库。 |
| ├─ **云函数** | Node.js 环境 | - 生成 VOD 上传签名<br>- 接收 VOD 的事件回调通知<br>- 调用翻译 API<br>- 读写数据库 |
| ├─ **云数据库** | NoSQL 数据库 | 存储用户信息、任务状态、VOD 文件 ID 等。 |
| **视频核心服务** | **腾讯云点播 VOD** | **一站式视频处理中心**。负责视频上传、存储、AI处理、字幕合成、CDN分发。 |

#### 2.2. 数据处理流程图

```mermaid
graph TD
    subgraph "用户端 (uni-app)"
        A[用户选择视频] --> B{调用云函数<br>get-upload-signature};
        B --> C[获取上传签名];
        A & C --> D[使用VOD上传SDK<br>将视频直传VOD];
        D -- 上传成功 --> E[获取VOD FileId];
        E --> F{调用云函数<br>create-task};
    end

    subgraph "后端 (微信云开发 + VOD)"
        F --> G[在云数据库中<br>创建任务记录 (status: processing)];
        D -- 视频传入VOD --> H[自动触发VOD任务流];
        H -- 1. 语音识别 (ASR) --> I[生成.srt字幕文件];
        I -- 2. 字幕烧录 --> J[生成带字幕的新视频];
        J -- 任务完成 --> K[VOD发送事件通知];
        K --> L{云函数<br>handle-vod-callback};
        L --> M[更新数据库任务状态<br>(status: completed, 记录新视频FileId)];
    end

    subgraph "结果展示 (uni-app)"
        N[前端轮询/监听<br>数据库任务状态] -- 状态变为completed --> O[获取新视频FileId];
        O --> P{调用云函数<br>get-video-url};
        P --> Q[获取VOD播放URL];
        Q --> R[在<video>组件中<br>预览和下载];
    end
```

### 3. 详细实施步骤

#### 第 1 步：准备与配置 (一次性)

1.  **开通腾讯云 VOD 服务**:
    *   登录腾讯云控制台，搜索“云点播”，按指引开通服务。
    *   建议购买新用户专享的资源包，以降低初期成本。

2.  **获取 API 密钥**:
    *   在腾讯云控制台的 [API密钥管理](https://console.cloud.tencent.com/cam/capi) 页面，获取 `SecretId` 和 `SecretKey`。
    *   **安全操作**：将这两个密钥配置为微信云函数的**环境变量**，绝不能硬编码在代码中。例如，变量名为 `TENCENT_SECRET_ID` 和 `TENCENT_SECRET_KEY`。

3.  **配置 VOD 任务流 (核心)**:
    *   在 VOD 控制台，进入“视频处理设置” -> “任务流模板”。
    *   创建一个新的任务流，命名为 `SubtitleProcessing`。
    *   在该任务流中，添加以下子任务：
        *   **音频/视频内容识别 (ASR)**:
            *   选择 `ProcessMediaByUrl` 接口中的 `AiRecognitionTask`。
            *   设置识别类型为 `AsrFullText`（全文语音识别）。
            *   设置生成的字幕格式为 `srt`。
        *   **转码并烧录字幕**:
            *   添加一个转码任务（如转为高清 MP4）。
            *   在转码配置中，关联**字幕设置**。
            *   关键点：字幕来源选择**“新增字幕”**，并指定为上一步 ASR 任务生成的字幕文件。VOD 会自动关联。
    *   保存任务流，并记住该任务流的名称 (`SubtitleProcessing`)。

4.  **配置事件通知**:
    *   在 VOD 控制台，进入“回调设置”。
    *   设置回调模式为“可靠回调”。
    *   **回调URL**: 填写一个**HTTP 触发的云函数**的 URL（例如 `handle-vod-callback`，后续会创建）。
    *   **回调事件**: 勾选**“任务流状态变更”(`ProcedureStateChanged`)**。这是我们唯一需要关心的事件。

#### 第 2 步：前端开发 (uni-app)

1.  **安装 VOD 上传 SDK**:
    *   在 `uni-app` 项目中，通过 npm 安装小程序上传 SDK：`npm install vod-wx-sdk-v2`。
    *   在页面中引入 SDK。

2.  **视频上传逻辑**:
    *   用户点击上传按钮，调用 `uni.chooseVideo` 选择视频。
    *   **获取上传签名**：在选择视频后，调用一个云函数 `get-upload-signature` (详见第 3 步)，获取到 VOD 的上传签名 `signature`。
    *   **执行上传**:
        ```javascript
        import VodUploader from 'vod-wx-sdk-v2';

        // ...
        const uploader = VodUploader.start({
            mediaFile: videoInfo, // uni.chooseVideo 返回的视频信息
            getSignature: () => {
                // 返回从云函数 get-upload-signature 获取的签名
                return signature;
            },
            procedure: 'SubtitleProcessing', // 指定要触发的任务流名称！
            // 监听事件
            onProgress: (progress) => {
                console.log('上传进度', progress.percent);
                this.uploadPercent = progress.percent * 100;
            },
            onFinish: (result) => {
                console.log('上传完成', result);
                // result.fileId 就是视频在 VOD 中的唯一标识
                // 调用云函数 create-task，将 fileId 和用户信息存入数据库
                wx.cloud.callFunction({
                    name: 'create-task',
                    data: {
                        fileId: result.fileId
                    }
                });
            },
            // ... 其他事件监听
        });
        ```

3.  **结果展示与下载**:
    *   通过轮询或数据库实时监听，当任务状态变为 `completed` 时，获取到 `processedVideoFileId`。
    *   调用一个新的云函数 `get-video-url`，传入 `processedVideoFileId`。
    *   该云函数会返回一个有时效性的可播放 URL，将其赋值给 `<video>` 组件的 `src` 属性进行播放。
    *   下载逻辑不变 (`uni.downloadFile` -> `uni.saveVideoToPhotosAlbum`)。

#### 第 3 步：后端开发 (微信云函数)

1.  **`get-upload-signature` (获取上传签名)**
    *   **职责**: 为前端提供一次性的、安全的上传凭证。
    *   **实现**:
        *   安装腾讯云 VOD Node.js SDK: `npm install tencentcloud-sdk-nodejs`。
        *   代码逻辑：
            ```javascript
            const tencentcloud = require("tencentcloud-sdk-nodejs");
            const VodClient = tencentcloud.vod.v20180717.Client;

            exports.main = async (event, context) => {
                const clientConfig = {
                    credential: {
                        secretId: process.env.TENCENT_SECRET_ID, // 从环境变量读取
                        secretKey: process.env.TENCENT_SECRET_KEY,
                    },
                    region: "ap-guangzhou", // 你的 VOD 区域
                };
                const client = new VodClient(clientConfig);
                const params = {
                    "Procedure": "SubtitleProcessing" // 指定上传后要执行的任务流
                };
                // 调用 SDK 的 ApplyUpload 接口生成签名
                const result = await client.ApplyUpload(params);
                return result.Signature; // 返回签名给前端
            };
            ```

2.  **`create-task` (创建任务记录)**
    *   **职责**: 接收前端传来的 VOD `fileId`，在数据库中创建任务。
    *   **实现**:
        *   获取用户 `openid`。
        *   在 `tasks` 集合中插入一条新记录，包含 `_openid`, `originalVideoFileId` (即 VOD FileId), `status: 'processing'`, `createTime` 等。

3.  **`handle-vod-callback` (处理 VOD 回调)**
    *   **职责**: 监听 VOD 的处理结果，更新数据库。
    *   **配置**: 必须设置为**HTTP 触发**的云函数。
    *   **实现**:
        ```javascript
        exports.main = async (event, context) => {
            const body = JSON.parse(event.body);
            if (body.EventType === 'ProcedureStateChanged') {
                const taskResult = body.ProcedureStateChangeEvent;
                if (taskResult.Status === 'FINISH') {
                    const db = wx.cloud.database();
                    const originalFileId = taskResult.FileId;
                    const newVideoInfo = taskResult.MediaProcessResultSet[0]; // 假设第一个是转码烧录任务
                    const processedFileId = newVideoInfo.FileId;

                    // 根据原始 FileId 找到数据库中的任务
                    await db.collection('tasks').where({
                        originalVideoFileId: originalFileId
                    }).update({
                        data: {
                            status: 'completed',
                            processedVideoFileId: processedFileId,
                            finishTime: new Date()
                        }
                    });
                } else if (taskResult.Status === 'ABORT') {
                    // 处理失败情况
                }
            }
            return { code: 0, message: 'success' }; // 必须返回成功响应给VOD
        };
        ```

4.  **`get-video-url` (获取播放地址)**
    *   **职责**: 用 `FileId` 换取实际的播放 URL。
    *   **实现**:
        *   调用 VOD SDK 的 `DescribeMediaInfos` 接口。
        *   传入 `FileIds: [event.fileId]`。
        *   从返回结果中找到 `MediaInfoSet[0].BasicInfo.MediaUrl`，并返回给前端。

### 4. 修订后的数据库设计

`tasks` 集合需要增加一个字段来追踪 VOD 任务。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `_id` | String | 记录的唯一 ID |
| `_openid` | String | 用户 OpenID |
| `originalVideoFileId` | String | **VOD中的原始视频 FileId** |
| `processedVideoFileId` | String | **VOD中带字幕的新视频 FileId** |
| `status` | String | `processing`, `completed`, `failed` |
| `errorMsg` | String | 失败信息 |
| `createTime` | Date | 创建时间 |
| `finishTime` | Date | 完成时间 |
| `vodTaskId` | String | (可选) VOD 任务流 ID，用于调试追溯 |

### 5. 总结

采用腾讯云 VOD 的技术方案，虽然前期配置稍多，但优势极其明显：
*   **架构清晰**：业务逻辑（云函数）与媒体处理（VOD）完全解耦。
*   **高度自动化**：利用任务流，将复杂的视频处理步骤串联起来，无需人工干预。
*   **性能与体验更佳**：专业的上传 SDK 支持断点续传，CDN 加速保证了播放流畅。
*   **高可扩展性**：未来可以轻松在任务流中增加智能封面、视频审核、加密等高级功能。

此方案是构建专业视频应用的理想选择，强烈推荐采纳。