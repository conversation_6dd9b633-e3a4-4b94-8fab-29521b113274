<template>
  <view class="result-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">处理完成</text>
      <text class="page-subtitle">您的视频字幕已生成完成</text>
    </view>

    <!-- 视频播放器卡片 -->
    <view class="video-card card">
      <view class="card-header">
        <text class="card-title">视频预览</text>
        <view class="video-status">
          <text class="status-icon">✅</text>
          <text class="status-text">处理完成</text>
        </view>
      </view>

      <view class="card-body">
        <view class="video-wrapper">
          <video
            v-if="videoUrl"
            :src="videoUrl"
            controls
            class="result-video"
            :poster="videoPoster"
            show-fullscreen-btn
            show-play-btn
            show-center-play-btn
          ></video>
          <view v-else class="video-loading">
            <view class="loading-spinner"></view>
            <text class="loading-text">视频加载中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频信息卡片 -->
    <view class="info-card card">
      <view class="card-header">
        <text class="card-title">视频信息</text>
      </view>

      <view class="card-body">
        <view class="info-grid">
          <view class="info-item">
            <view class="info-icon">⏱️</view>
            <view class="info-content">
              <text class="info-label">处理时间</text>
              <text class="info-value">{{ formatTime(taskInfo.finishTime) }}</text>
            </view>
          </view>
          <view class="info-item">
            <view class="info-icon">🎬</view>
            <view class="info-content">
              <text class="info-label">视频时长</text>
              <text class="info-value">{{ formatDuration(taskInfo.duration) }}</text>
            </view>
          </view>
          <view class="info-item">
            <view class="info-icon">📁</view>
            <view class="info-content">
              <text class="info-label">文件大小</text>
              <text class="info-value">{{ formatFileSize(taskInfo.fileSize) }}</text>
            </view>
          </view>
          <view class="info-item">
            <view class="info-icon">📝</view>
            <view class="info-content">
              <text class="info-label">字幕数量</text>
              <text class="info-value">{{ subtitleList.length }} 条</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions-card card">
      <view class="card-header">
        <text class="card-title">操作选项</text>
      </view>

      <view class="card-body">
        <view class="action-buttons">
          <button @click="downloadVideo" class="btn btn-primary btn-lg">
            <text class="btn-icon">⬇️</text>
            <text>下载视频</text>
          </button>
          <view class="button-row">
            <button @click="shareVideo" class="btn btn-secondary">
              <text class="btn-icon">📤</text>
              <text>分享视频</text>
            </button>
            <button @click="viewSubtitle" class="btn btn-outline">
              <text class="btn-icon">📝</text>
              <text>查看字幕</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 字幕预览弹窗 -->
    <view v-if="showSubtitleModal" class="subtitle-modal" @click="closeSubtitleModal">
      <view class="subtitle-content card" @click.stop>
        <view class="card-header">
          <text class="card-title">字幕内容</text>
          <button @click="closeSubtitleModal" class="btn btn-ghost btn-sm close-btn">
            <text>✕</text>
          </button>
        </view>

        <view class="card-body">
          <scroll-view class="subtitle-list" scroll-y>
            <view v-for="(item, index) in subtitleList" :key="index" class="subtitle-item">
              <view class="subtitle-time-wrapper">
                <text class="subtitle-time">{{ item.startTime }} → {{ item.endTime }}</text>
              </view>
              <text class="subtitle-text">{{ item.text }}</text>
            </view>
            <view v-if="subtitleList.length === 0" class="empty-subtitle">
              <text class="empty-icon">📝</text>
              <text class="empty-text">暂无字幕内容</text>
            </view>
          </scroll-view>
        </view>

        <view class="card-footer">
          <view class="subtitle-actions">
            <button @click="copySubtitle" class="btn btn-secondary">
              <text class="btn-icon">📋</text>
              <text>复制字幕</text>
            </button>
            <button @click="downloadSubtitle" class="btn btn-primary">
              <text class="btn-icon">⬇️</text>
              <text>下载SRT</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getTaskDetail, getVideoUrl, getSubtitle, getSubtitleFile } from '@/utils/api'

// 响应式数据
const videoUrl = ref('')
const videoPoster = ref('')
const taskInfo = ref<any>({})
const showSubtitleModal = ref(false)
const subtitleList = ref<any[]>([])
const taskId = ref('')

// 页面加载时获取参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  taskId.value = options.taskId || ''
  
  if (taskId.value) {
    loadTaskResult()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})

// 加载任务结果
const loadTaskResult = async () => {
  try {
    uni.showLoading({ title: '加载中...' })

    // 获取任务详情
    taskInfo.value = await getTaskDetail(taskId.value)

    // 获取视频播放URL
    const videoResult = await getVideoUrl(taskInfo.value.processedVideoFileId)

    videoUrl.value = videoResult.url
    videoPoster.value = videoResult.poster || ''

    // 获取字幕内容
    try {
      subtitleList.value = await getSubtitle(taskId.value)
    } catch (error) {
      console.error('获取字幕失败:', error)
      subtitleList.value = []
    }

    uni.hideLoading()
  } catch (error) {
    console.error('加载结果失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 下载视频
const downloadVideo = () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频未加载',
      icon: 'none'
    })
    return
  }
  
  uni.showLoading({ title: '下载中...' })
  
  uni.downloadFile({
    url: videoUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveVideoToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            console.error('保存失败:', err)
            uni.hideLoading()
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 分享视频
const shareVideo = () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频未加载',
      icon: 'none'
    })
    return
  }

  // 小程序环境下的分享
  uni.showShareMenu({
    withShareTicket: true,
    success: () => {
      uni.showToast({
        title: '请点击右上角分享',
        icon: 'none'
      })
    },
    fail: (err) => {
      console.error('分享失败:', err)
      uni.showToast({
        title: '分享功能暂不可用',
        icon: 'none'
      })
    }
  })
}

// 查看字幕
const viewSubtitle = async () => {
  try {
    uni.showLoading({ title: '加载字幕...' })
    
    subtitleList.value = await getSubtitle(taskId.value)
    showSubtitleModal.value = true
    
    uni.hideLoading()
  } catch (error) {
    console.error('加载字幕失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载字幕失败',
      icon: 'none'
    })
  }
}

// 关闭字幕弹窗
const closeSubtitleModal = () => {
  showSubtitleModal.value = false
}

// 复制字幕
const copySubtitle = () => {
  const subtitleText = subtitleList.value
    .map(item => `${item.startTime} --> ${item.endTime}\n${item.text}`)
    .join('\n\n')
  
  uni.setClipboardData({
    data: subtitleText,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  })
}

// 下载字幕文件
const downloadSubtitle = async () => {
  try {
    await getSubtitleFile(taskId.value)
    
    // 这里可以实现SRT文件下载逻辑
    uni.showToast({
      title: '字幕文件已生成',
      icon: 'success'
    })
  } catch (error) {
    console.error('下载字幕失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 格式化时长
const formatDuration = (duration: number): string => {
  if (!duration) return ''
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return ''
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
  padding-bottom: 120rpx; /* 为tabbar留出空间 */
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 视频卡片 */
.video-card {
  margin-bottom: 48rpx;
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-icon {
  font-size: 24rpx;
}

.status-text {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 500;
}

.video-wrapper {
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #f3f4f6;
}

.result-video {
  width: 100%;
  height: 400rpx;
  background-color: #000;
}

.video-loading {
  height: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* 信息卡片 */
.info-card {
  margin-bottom: 48rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.info-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-label {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
  margin-bottom: 4rpx;
}

.info-value {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}

/* 操作卡片 */
.actions-card {
  margin-bottom: 48rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.button-row {
  display: flex;
  gap: 16rpx;
}

.button-row .btn {
  flex: 1;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 字幕弹窗 */
.subtitle-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.subtitle-content {
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 80rpx rgba(0, 0, 0, 0.3);
}

.subtitle-content .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.subtitle-list {
  flex: 1;
  max-height: 400rpx;
}

.subtitle-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-time-wrapper {
  margin-bottom: 12rpx;
}

.subtitle-time {
  display: inline-block;
  font-size: 22rpx;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-family: monospace;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
}

.empty-subtitle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

.subtitle-actions {
  display: flex;
  gap: 16rpx;
}
</style>
