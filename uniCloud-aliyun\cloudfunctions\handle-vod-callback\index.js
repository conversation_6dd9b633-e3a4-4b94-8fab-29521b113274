// uniCloud云函数：处理VOD回调
'use strict';

/**
 * 处理腾讯云VOD的事件回调通知
 * 需要配置为HTTP触发的云函数
 */
exports.main = async (event, context) => {
  try {
    console.log('收到VOD回调:', event)
    
    // 解析HTTP请求体
    let body
    if (typeof event.body === 'string') {
      body = JSON.parse(event.body)
    } else {
      body = event.body
    }
    
    // 验证回调事件类型
    if (body.EventType !== 'ProcedureStateChanged') {
      console.log('非任务流状态变更事件，忽略')
      return {
        statusCode: 200,
        body: JSON.stringify({ code: 0, message: 'ignored' })
      }
    }
    
    const taskResult = body.ProcedureStateChangeEvent
    console.log('任务流状态变更:', taskResult)
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    if (taskResult.Status === 'FINISH') {
      // 任务完成，更新数据库
      const originalFileId = taskResult.FileId
      
      // 查找处理结果中的转码任务
      let processedFileId = null
      if (taskResult.MediaProcessResultSet && taskResult.MediaProcessResultSet.length > 0) {
        // 假设第一个是转码烧录任务
        const transcodeResult = taskResult.MediaProcessResultSet[0]
        if (transcodeResult.Type === 'Transcode' && transcodeResult.TranscodeTask) {
          processedFileId = transcodeResult.TranscodeTask.Output.FileId
        }
      }
      
      // 更新任务状态
      const updateResult = await db.collection('tasks').where({
        originalVideoFileId: originalFileId
      }).update({
        status: 'completed',
        processedVideoFileId: processedFileId,
        finishTime: new Date(),
        vodTaskId: taskResult.TaskId
      })
      
      console.log('任务状态更新成功:', updateResult)
      
    } else if (taskResult.Status === 'ABORT') {
      // 任务失败，更新数据库
      const originalFileId = taskResult.FileId
      const errorMsg = taskResult.ErrCodeExt || '处理失败'
      
      await db.collection('tasks').where({
        originalVideoFileId: originalFileId
      }).update({
        status: 'failed',
        errorMsg: errorMsg,
        finishTime: new Date(),
        vodTaskId: taskResult.TaskId
      })
      
      console.log('任务失败，状态已更新')
    }
    
    // 返回成功响应给VOD
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        code: 0, 
        message: 'success' 
      })
    }
    
  } catch (error) {
    console.error('处理VOD回调失败:', error)
    
    // 即使处理失败，也要返回成功状态，避免VOD重复回调
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        code: -1, 
        message: 'internal error' 
      })
    }
  }
}
