<template>
  <view class="video-uploader card">
    <view class="card-body">
      <!-- 上传区域 -->
      <view
        class="upload-area"
        :class="{
          'upload-area-active': isDragOver,
          'upload-area-disabled': uploading,
          'upload-area-success': uploadSuccess
        }"
        @click="chooseVideo"
      >
        <view class="upload-content">
          <view class="upload-icon-wrapper">
            <text v-if="!uploading && !uploadSuccess" class="upload-icon">📹</text>
            <view v-else-if="uploading" class="loading-spinner"></view>
            <text v-else class="success-icon">✅</text>
          </view>

          <view class="upload-text">
            <text v-if="!uploading && !uploadSuccess" class="upload-main-text">
              {{ videoInfo ? '重新选择视频' : '点击选择视频文件' }}
            </text>
            <text v-else-if="uploading" class="upload-main-text">正在上传...</text>
            <text v-else class="upload-main-text">上传成功</text>

            <text v-if="!uploading && !uploadSuccess" class="upload-sub-text">
              支持 MP4、MOV、AVI 等格式，最大 {{ maxSizeMB }}MB
            </text>
            <text v-else-if="uploading" class="upload-sub-text">
              {{ uploadPercent }}% - 请保持网络连接
            </text>
            <text v-else class="upload-sub-text">
              文件已成功上传到云端
            </text>
          </view>
        </view>

        <!-- 进度条 -->
        <view v-if="uploading" class="upload-progress">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: uploadPercent + '%' }"></view>
          </view>
        </view>
      </view>

      <!-- 视频预览 -->
      <view v-if="videoInfo && !uploading" class="video-preview">
        <video
          :src="videoInfo.tempFilePath"
          controls
          class="preview-video"
          :poster="videoInfo.thumbTempFilePath"
          show-center-play-btn
          show-fullscreen-btn
        ></video>

        <view class="video-info">
          <view class="info-row">
            <text class="info-label">文件名</text>
            <text class="info-value">{{ getFileName() }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">文件大小</text>
            <text class="info-value">{{ formatFileSize(videoInfo.size) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">视频时长</text>
            <text class="info-value">{{ formatDuration(videoInfo.duration) }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view v-if="videoInfo && !uploading && !uploadSuccess" class="upload-actions">
        <button @click="startUpload" class="btn btn-primary btn-lg">
          <text class="btn-icon">🚀</text>
          <text>开始上传</text>
        </button>
        <button @click="resetVideo" class="btn btn-secondary">
          <text class="btn-icon">🔄</text>
          <text>重新选择</text>
        </button>
      </view>

      <!-- 上传成功操作 -->
      <view v-if="uploadSuccess" class="success-actions">
        <button @click="$emit('upload-success', uploadResult)" class="btn btn-primary btn-lg">
          <text class="btn-icon">➡️</text>
          <text>继续处理</text>
        </button>
        <button @click="resetUploader" class="btn btn-outline">
          <text class="btn-icon">📹</text>
          <text>上传新视频</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VodUploader from 'vod-wx-sdk-v2'
import { formatFileSize, formatDuration, validateVideoFile, handleError, showSuccess } from '@/utils/common'
import { getUploadSignature, createTask } from '@/utils/api'

// Props
interface Props {
  maxSizeMB?: number
  maxDuration?: number
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxSizeMB: 100,
  maxDuration: 300,
  autoUpload: false
})

// Emits
interface Emits {
  (e: 'upload-start', fileInfo: any): void
  (e: 'upload-progress', percent: number): void
  (e: 'upload-success', result: any): void
  (e: 'upload-error', error: any): void
  (e: 'video-selected', videoInfo: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const videoInfo = ref<any>(null)
const uploading = ref(false)
const uploadSuccess = ref(false)
const uploadPercent = ref(0)
const isDragOver = ref(false)
const uploadResult = ref<any>(null)

// 计算属性
const maxSizeBytes = computed(() => props.maxSizeMB * 1024 * 1024)

// 选择视频
const chooseVideo = () => {
  if (uploading.value) return

  uni.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: props.maxDuration,
    success: (res) => {
      console.log('选择视频成功:', res)

      // 验证文件大小
      if (res.size > maxSizeBytes.value) {
        uni.showToast({
          title: `文件大小不能超过${props.maxSizeMB}MB`,
          icon: 'none'
        })
        return
      }

      // 验证时长
      if (res.duration > props.maxDuration) {
        uni.showToast({
          title: `视频时长不能超过${Math.floor(props.maxDuration / 60)}分钟`,
          icon: 'none'
        })
        return
      }

      videoInfo.value = res
      uploadSuccess.value = false
      emit('video-selected', videoInfo.value)
    },
    fail: (err) => {
      console.error('选择视频失败:', err)
      uni.showToast({
        title: '选择视频失败',
        icon: 'none'
      })
    }
  })
}

// 开始上传
const startUpload = async () => {
  if (!videoInfo.value || uploading.value) return

  uploading.value = true
  uploadPercent.value = 0

  try {
    emit('upload-start', videoInfo.value)

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadPercent.value < 90) {
        uploadPercent.value += Math.random() * 10
        emit('upload-progress', uploadPercent.value)
      }
    }, 500)

    // 实际上传逻辑（这里需要根据实际API调整）
    const uploadTask = uni.uploadFile({
      url: 'your-upload-api-url',
      filePath: videoInfo.value.tempFilePath,
      name: 'video',
      success: (res) => {
        clearInterval(progressInterval)
        uploadPercent.value = 100
        uploadSuccess.value = true
        uploadResult.value = res
        emit('upload-success', res)

        uni.showToast({
          title: '上传成功',
          icon: 'success'
        })
      },
      fail: (err) => {
        clearInterval(progressInterval)
        console.error('上传失败:', err)
        emit('upload-error', err.errMsg || '上传失败')

        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    })

    // 监听上传进度
    uploadTask.onProgressUpdate((res) => {
      uploadPercent.value = res.progress
      emit('upload-progress', res.progress)
    })

  } catch (error) {
    console.error('上传异常:', error)
    emit('upload-error', '上传异常')
  } finally {
    uploading.value = false
  }
}

// 重置视频
const resetVideo = () => {
  videoInfo.value = null
  uploadSuccess.value = false
  uploadPercent.value = 0
}

// 重置上传器
const resetUploader = () => {
  resetVideo()
  uploadResult.value = null
}

// 获取文件名
const getFileName = (): string => {
  if (!videoInfo.value?.tempFilePath) return '未知文件'
  const path = videoInfo.value.tempFilePath
  return path.split('/').pop() || '未知文件'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}

// 格式化时长
const formatDuration = (duration: number): string => {
  if (!duration) return '00:00'
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.video-uploader {
  margin-bottom: 32rpx;
}

/* 上传区域 */
.upload-area {
  border: 3rpx dashed #d1d5db;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background: linear-gradient(135deg, #fafafa, #f5f5f5);
}

.upload-area:active {
  border-color: #6366f1;
  background: linear-gradient(135deg, #f8faff, #f0f4ff);
}
}

.upload-area-active {
  border-color: #6366f1;
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  transform: scale(1.02);
}

.upload-area-disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.upload-area-disabled:active {
  border-color: #d1d5db;
  background: linear-gradient(135deg, #fafafa, #f5f5f5);
  transform: none;
}

.upload-area-success {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24rpx;
}

.upload-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.2);
}

.upload-icon {
  font-size: 60rpx;
}

.success-icon {
  font-size: 60rpx;
  color: #10b981;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-text {
  text-align: center;
}

.upload-main-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.upload-sub-text {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 进度条 */
.upload-progress {
  margin-top: 24rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 视频预览 */
.video-preview {
  margin-top: 32rpx;
}

.preview-video {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #f3f4f6;
  margin-bottom: 24rpx;
}

.video-info {
  background: linear-gradient(135deg, #f8faff, #f0f4ff);
  border-radius: 16rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}
}

.info-label {
  font-size: 26rpx;
  color: #6b7280;
}

.info-value {
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 操作按钮 */
.upload-actions,
.success-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.upload-actions .btn:first-child,
.success-actions .btn:first-child {
  flex: 2;
}

.upload-actions .btn:last-child,
.success-actions .btn:last-child {
  flex: 1;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}
</style>
