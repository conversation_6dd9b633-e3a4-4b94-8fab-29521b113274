# 微信登录功能部署说明

## 功能概述

本次更新为 `handleWechatLogin` 函数添加了获取微信用户 openid 的功能，实现了完整的微信登录流程。

## 新增功能

### 1. 云函数
- **get-wechat-openid**: 通过微信登录 code 获取用户 openid
- **update-user-info**: 更新用户昵称和头像信息

### 2. 数据库表
- **users**: 用户信息表，存储 openid、昵称、头像等信息

### 3. API 接口
- `getWechatOpenid(code: string)`: 获取微信用户 openid
- `updateUserInfo(openid: string, nickname?: string, avatar?: string)`: 更新用户信息

## 部署步骤

### 第一步：配置环境变量

在 uniCloud 控制台的云函数环境变量中配置：

```
WX_APPID=你的小程序AppID
WX_APPSECRET=你的小程序AppSecret
```

### 第二步：部署云函数

1. 部署 `get-wechat-openid` 云函数
2. 部署 `update-user-info` 云函数

### 第三步：创建数据库表

在 uniCloud 控制台创建 `users` 集合，字段结构参考 `users.schema.json`

### 第四步：测试功能

1. 在小程序中点击"微信登录"按钮
2. 授权后查看控制台日志
3. 验证用户信息是否正确存储

## 接口设计规范

### get-wechat-openid 接口

**请求方法**: 云函数调用
**请求参数**:
```typescript
{
  code: string,      // 微信登录返回的code
  deviceId: string   // 设备唯一标识
}
```

**返回格式**:
```typescript
{
  errCode: number,
  errMsg: string,
  data?: {
    openid: string,
    userId: string,
    sessionKey: string,
    unionid?: string,
    isNewUser: boolean
  }
}
```

### update-user-info 接口

**请求方法**: 云函数调用
**请求参数**:
```typescript
{
  openid: string,     // 用户openid
  nickname?: string,  // 用户昵称
  avatar?: string,    // 用户头像URL
  deviceId: string    // 设备唯一标识
}
```

**返回格式**:
```typescript
{
  errCode: number,
  errMsg: string,
  data?: {
    updated: number,
    updateData: object
  }
}
```

## 代码变更说明

### 1. handleWechatLogin 函数优化

- 添加了调用服务端获取 openid 的步骤
- 增加了用户信息更新到服务端的逻辑
- 改进了错误处理和用户提示
- 保存 openid 到本地存储

### 2. 用户信息结构扩展

- 添加了 `openid` 字段
- 本地存储增加了 `isNewUser` 标识

### 3. API 工具类扩展

- 新增 `getWechatOpenid` 方法
- 新增 `updateUserInfo` 方法
- 添加相关 TypeScript 接口定义

## 错误处理

### 常见错误码

- `INVALID_PARAM`: 参数错误
- `WX_API_ERROR`: 微信接口调用失败
- `NO_OPENID`: 未获取到 openid
- `USER_NOT_FOUND`: 用户不存在
- `GET_OPENID_FAILED`: 获取 openid 失败
- `UPDATE_USER_FAILED`: 更新用户信息失败

### 微信接口错误码

- `40013`: 无效的 AppID
- `40014`: 无效的 AppSecret
- `40029`: 无效的 code
- `45011`: API 调用太频繁

## 安全注意事项

1. **环境变量保护**: AppID 和 AppSecret 必须配置在云函数环境变量中，不能硬编码
2. **参数验证**: 所有云函数都进行了严格的参数验证
3. **错误信息**: 避免在错误信息中暴露敏感信息
4. **会话管理**: sessionKey 仅用于服务端，不返回给前端

## 测试建议

1. **单元测试**: 为云函数编写单元测试
2. **集成测试**: 测试完整的登录流程
3. **错误测试**: 测试各种错误情况的处理
4. **性能测试**: 测试并发登录的性能

## 后续优化建议

1. **缓存优化**: 可以考虑缓存用户信息减少数据库查询
2. **日志监控**: 添加详细的日志记录和监控
3. **用户体验**: 可以添加登录状态的持久化
4. **安全加固**: 可以添加防刷机制和频率限制
