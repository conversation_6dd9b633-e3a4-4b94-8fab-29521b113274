<template>
  <view class="profile-container">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="header-background"></view>
      <view class="user-content">
        <view class="avatar-section">
          <view class="avatar-wrapper">
            <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.svg'" mode="aspectFill" />
            <view v-if="userInfo.isLogin" class="online-indicator"></view>
          </view>
          <view class="user-info">
            <text class="username">{{ userInfo.nickname || '未登录用户' }}</text>
            <text class="user-id">{{ getUserIdText() }}</text>
          </view>
        </view>
        <button v-if="!userInfo.isLogin" @click="handleWechatLogin" class="btn btn-primary login-btn">
          <text class="btn-icon">👤</text>
          <text>微信登录</text>
        </button>
      </view>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-card card">
      <view class="card-header">
        <text class="card-title">使用统计</text>
      </view>
      <view class="card-body">
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-icon">📊</view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.totalTasks }}</text>
              <text class="stat-label">处理任务</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">✅</view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.completedTasks }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">⏱️</view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.totalDuration }}</text>
              <text class="stat-label">总时长(分钟)</text>
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">📁</view>
            <view class="stat-content">
              <text class="stat-number">{{ formatFileSize(stats.totalSize) }}</text>
              <text class="stat-label">处理文件</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-card card">
      <view class="card-header">
        <text class="card-title">功能菜单</text>
      </view>
      <view class="card-body">
        <view class="menu-list">
          <view class="menu-item" @click="navigateTo('/pages/history/history')">
            <view class="menu-icon-wrapper">
              <text class="menu-icon">📋</text>
            </view>
            <view class="menu-content">
              <text class="menu-text">历史记录</text>
              <text class="menu-desc">查看处理历史</text>
            </view>
            <text class="menu-arrow">→</text>
          </view>

          <view class="menu-item" @click="showSettings">
            <view class="menu-icon-wrapper">
              <text class="menu-icon">⚙️</text>
            </view>
            <view class="menu-content">
              <text class="menu-text">设置</text>
              <text class="menu-desc">个性化设置</text>
            </view>
            <text class="menu-arrow">→</text>
          </view>

          <view class="menu-item" @click="showAbout">
            <view class="menu-icon-wrapper">
              <text class="menu-icon">ℹ️</text>
            </view>
            <view class="menu-content">
              <text class="menu-text">关于我们</text>
              <text class="menu-desc">了解更多信息</text>
            </view>
            <text class="menu-arrow">→</text>
          </view>

          <view class="menu-item" @click="showHelp">
            <view class="menu-icon-wrapper">
              <text class="menu-icon">❓</text>
            </view>
            <view class="menu-content">
              <text class="menu-text">帮助与反馈</text>
              <text class="menu-desc">获取帮助支持</text>
            </view>
            <text class="menu-arrow">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-card">
      <text class="version-text">智能字幕胶囊 v1.0.0</text>
      <text class="copyright-text">© 2024 All Rights Reserved</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getHistoryList, getWechatOpenid, updateUserInfo } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  totalDuration: 0,
  totalSize: 0
})

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  await loadStats()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取用户ID显示文本
const getUserIdText = (): string => {
  if (userInfo.value.isLogin) {
    return `ID: ${userInfo.value.deviceId?.slice(-8) || 'N/A'}`
  }
  return '点击登录获取更多功能'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 加载统计数据
const loadStats = async () => {
  try {
    const historyList = await getHistoryList(0, 100)
    stats.value.totalTasks = historyList.length
    stats.value.completedTasks = historyList.filter(task => task.status === 'completed').length

    // 计算总时长和总文件大小
    stats.value.totalDuration = Math.round(historyList.reduce((total, task) => {
      return total + (task.duration || 0)
    }, 0) / 60) // 转换为分钟

    stats.value.totalSize = historyList.reduce((total, task) => {
      return total + (task.fileSize || 0)
    }, 0)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 微信登录
const handleWechatLogin = async () => {
  try {
    uni.showLoading({ title: '登录中...' })

    // 第一步：调用 uni.login 获取 code
    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    console.log('获取到微信登录code:', loginRes.code)

    // 第二步：调用服务端接口获取 openid
    uni.showLoading({ title: '获取用户信息中...' })

    const openidResult = await getWechatOpenid(loginRes.code)
    console.log('获取openid结果:', openidResult)

    // 验证返回结果
    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    if (!openidResult.data || !openidResult.data.openid) {
      throw new Error('获取用户openid失败')
    }

    const { openid, userId, isNewUser } = openidResult.data

    // 第三步：获取用户信息授权
    uni.showLoading({ title: '获取用户资料中...' })

    const userProfile = await new Promise<any>((resolve, reject) => {
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })

    // 第四步：更新服务端用户信息
    if (userProfile.userInfo.nickName || userProfile.userInfo.avatarUrl) {
      try {
        await updateUserInfo(
          openid,
          userProfile.userInfo.nickName,
          userProfile.userInfo.avatarUrl
        )
        console.log('用户信息更新成功')
      } catch (updateError) {
        console.warn('更新用户信息失败，但不影响登录:', updateError)
      }
    }

    // 第五步：更新本地用户信息
    userInfo.value.nickname = userProfile.userInfo.nickName
    userInfo.value.avatar = userProfile.userInfo.avatarUrl
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)

    uni.hideLoading()

    // 显示登录成功提示
    const successMessage = isNewUser ? '注册成功' : '登录成功'
    uni.showToast({
      title: successMessage,
      icon: 'success'
    })

    console.log('微信登录完成:', {
      openid: openid,
      nickname: userInfo.value.nickname,
      isNewUser: isNewUser
    })

  } catch (error: any) {
    uni.hideLoading()
    console.error('微信登录失败:', error)

    let errorMessage = '登录失败'

    // 处理不同类型的错误
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMessage = '用户拒绝授权'
      } else if (error.errMsg.includes('auth cancel')) {
        errorMessage = '用户取消登录'
      }
    } else if (error.message) {
      if (error.message.includes('获取登录凭证失败')) {
        errorMessage = '获取登录凭证失败，请重试'
      } else if (error.message.includes('获取用户openid失败')) {
        errorMessage = '获取用户信息失败，请重试'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
  }
}

// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示设置
const showSettings = () => {
  uni.showModal({
    title: '设置',
    content: '设置功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系我们的客服团队。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding-bottom: 120rpx;
  /* 为tabbar留出空间 */
}

/* 用户头部 */
.user-header {
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  padding: 80rpx 32rpx 48rpx;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.user-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-wrapper {
  position: relative;
  margin-right: 32rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #10b981;
  border-radius: 50%;
  border: 3rpx solid white;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.username {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.login-btn {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.login-btn .btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 统计卡片 */
.stats-card {
  margin: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

/* 菜单卡片 */
.menu-card {
  margin: 32rpx;
}

.menu-list {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
  transition: all 0.3s ease;
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f3f4f6;
}

.menu-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.menu-icon {
  font-size: 32rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #9ca3af;
}

.menu-arrow {
  font-size: 28rpx;
  color: #9ca3af;
  font-weight: 600;
}

/* 版本信息 */
.version-card {
  text-align: center;
  padding: 48rpx 32rpx;
}

.version-text {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.copyright-text {
  display: block;
  font-size: 22rpx;
  color: #9ca3af;
}
</style>
