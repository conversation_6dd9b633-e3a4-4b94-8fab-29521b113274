{"bsonType": "object", "description": "视频处理任务记录表", "required": ["deviceId", "originalVideoFileId", "status", "createTime"], "properties": {"_id": {"description": "记录ID"}, "deviceId": {"bsonType": "string", "description": "设备唯一标识", "title": "设备ID"}, "originalVideoFileId": {"bsonType": "string", "description": "原始视频FileId", "title": "原始视频ID"}, "processedVideoFileId": {"bsonType": "string", "description": "处理后视频FileId", "title": "处理后视频ID"}, "status": {"bsonType": "string", "description": "任务状态", "title": "状态", "enum": ["processing", "completed", "failed"], "enumDesc": ["处理中", "已完成", "失败"]}, "errorMsg": {"bsonType": "string", "description": "错误信息", "title": "错误信息"}, "createTime": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间"}, "finishTime": {"bsonType": "timestamp", "description": "完成时间", "title": "完成时间"}, "fileName": {"bsonType": "string", "description": "文件名", "title": "文件名"}, "fileSize": {"bsonType": "int", "description": "文件大小(字节)", "title": "文件大小"}, "duration": {"bsonType": "int", "description": "视频时长(秒)", "title": "视频时长"}, "clientIP": {"bsonType": "string", "description": "客户端IP", "title": "客户端IP"}, "userAgent": {"bsonType": "string", "description": "用户代理", "title": "用户代理"}}, "permission": {"read": true, "create": true, "update": false, "delete": false}}