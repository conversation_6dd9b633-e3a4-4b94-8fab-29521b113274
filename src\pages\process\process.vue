<template>
  <view class="process-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">处理进度</text>
      <text class="page-subtitle">AI正在为您的视频生成字幕</text>
    </view>

    <!-- 状态卡片 -->
    <view class="status-card card">
      <view class="card-body">
        <view class="status-content">
          <view class="status-icon-wrapper">
            <view class="status-icon" :class="getStatusIconClass()">
              <text v-if="taskStatus === 'processing'" class="processing-icon">⚡</text>
              <text v-else-if="taskStatus === 'completed'" class="success-icon">✅</text>
              <text v-else-if="taskStatus === 'failed'" class="error-icon">❌</text>
              <text v-else class="pending-icon">⏸️</text>
            </view>
          </view>

          <view class="status-text">
            <text class="status-title">{{ getStatusTitle() }}</text>
            <text class="status-desc">{{ getStatusDesc() }}</text>
          </view>
        </view>

        <!-- 进度条 -->
        <view v-if="taskStatus === 'processing'" class="progress-section">
          <view class="progress-header">
            <text class="progress-label">整体进度</text>
            <text class="progress-percent">{{ getProgressPercent() }}%</text>
          </view>
          <view class="progress-bar-wrapper">
            <view class="progress-bar">
              <view
                class="progress-fill"
                :style="{ width: getProgressPercent() + '%' }"
              ></view>
            </view>
          </view>
          <text class="progress-text">预计还需 {{ getEstimatedTime() }} 分钟</text>
        </view>
      </view>
    </view>

    <!-- 处理步骤 -->
    <view class="steps-card card">
      <view class="card-header">
        <text class="card-title">处理步骤</text>
      </view>

      <view class="card-body">
        <view class="steps-container">
          <view
            v-for="(step, index) in steps"
            :key="index"
            class="step-item"
            :class="{
              active: currentStep >= index + 1,
              completed: currentStep > index + 1,
              processing: currentStep === index + 1 && taskStatus === 'processing'
            }"
          >
            <view class="step-indicator">
              <view class="step-number" :class="getStepNumberClass(index)">
                <text v-if="currentStep > index + 1">✓</text>
                <text v-else-if="currentStep === index + 1 && taskStatus === 'processing'" class="step-loading">⟳</text>
                <text v-else>{{ index + 1 }}</text>
              </view>
              <view v-if="index < steps.length - 1" class="step-connector" :class="{ active: currentStep > index + 1 }"></view>
            </view>

            <view class="step-content">
              <text class="step-title">{{ step.title }}</text>
              <text class="step-desc">{{ step.desc }}</text>
              <text v-if="step.time" class="step-time">{{ step.time }}</text>
              <view v-if="currentStep === index + 1 && taskStatus === 'processing'" class="step-progress">
                <view class="step-progress-bar">
                  <view class="step-progress-fill" :style="{ width: getStepProgress(index) + '%' }"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 错误信息 -->
    <view v-if="errorMsg" class="error-card card">
      <view class="card-body">
        <view class="error-content">
          <text class="error-icon">⚠️</text>
          <view class="error-text">
            <text class="error-title">处理失败</text>
            <text class="error-message">{{ errorMsg }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button
        v-if="taskStatus === 'completed'"
        @click="viewResult"
        class="btn btn-primary btn-lg"
      >
        <text class="btn-icon">👀</text>
        <text>查看结果</text>
      </button>
      <button
        v-if="taskStatus === 'failed'"
        @click="retryProcess"
        class="btn btn-primary btn-lg"
      >
        <text class="btn-icon">🔄</text>
        <text>重新处理</text>
      </button>
      <button @click="goBack" class="btn btn-secondary">返回首页</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getTaskStatus } from '@/utils/api'

// 响应式数据
const taskStatus = ref('processing') // processing, completed, failed
const currentStep = ref(1)
const errorMsg = ref('')
const fileId = ref('')
const taskId = ref('')
const startTime = ref(Date.now())

// 处理步骤数据
const steps = ref([
  {
    title: '视频上传',
    desc: '视频文件上传到云端',
    time: ''
  },
  {
    title: '语音识别',
    desc: 'AI识别视频中的语音内容',
    time: ''
  },
  {
    title: '字幕生成',
    desc: '生成SRT字幕文件',
    time: ''
  },
  {
    title: '视频合成',
    desc: '将字幕烧录到视频中',
    time: ''
  }
])

// 轮询定时器
let pollTimer: any = null

// 页面加载时获取参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  fileId.value = options.fileId || ''
  
  if (fileId.value) {
    startPolling()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})

// 页面卸载时清除定时器
onUnmounted(() => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
})

// 开始轮询任务状态
const startPolling = () => {
  // 立即查询一次
  checkTaskStatus()
  
  // 每3秒轮询一次
  pollTimer = setInterval(() => {
    if (taskStatus.value === 'processing') {
      checkTaskStatus()
    } else {
      clearInterval(pollTimer)
    }
  }, 3000)
}

// 检查任务状态
const checkTaskStatus = async () => {
  try {
    const task = await getTaskStatus(fileId.value)
    if (task) {
      taskId.value = task._id
      taskStatus.value = task.status
      errorMsg.value = task.errorMsg || ''
      
      // 根据状态更新步骤
      updateCurrentStep(task.status)
    }
  } catch (error) {
    console.error('查询任务状态失败:', error)
  }
}

// 更新当前步骤
const updateCurrentStep = (status: string) => {
  switch (status) {
    case 'processing':
      // 可以根据更详细的状态来设置步骤
      currentStep.value = 2
      break
    case 'completed':
      currentStep.value = 4
      break
    case 'failed':
      // 保持当前步骤不变
      break
  }
}

// 获取状态标题
const getStatusTitle = (): string => {
  switch (taskStatus.value) {
    case 'processing':
      return '正在处理中...'
    case 'completed':
      return '处理完成'
    case 'failed':
      return '处理失败'
    default:
      return '未知状态'
  }
}

// 获取状态描述
const getStatusDesc = (): string => {
  switch (taskStatus.value) {
    case 'processing':
      return '请耐心等待，通常需要1-3分钟'
    case 'completed':
      return '视频字幕已生成完成'
    case 'failed':
      return '处理过程中出现错误'
    default:
      return ''
  }
}

// 查看结果
const viewResult = () => {
  uni.navigateTo({
    url: `/pages/result/result?taskId=${taskId.value}`
  })
}

// 重新处理
const retryProcess = () => {
  uni.showModal({
    title: '确认重新处理',
    content: '是否重新处理该视频？',
    success: (res) => {
      if (res.confirm) {
        // 重新开始处理
        taskStatus.value = 'processing'
        currentStep.value = 1
        errorMsg.value = ''
        startPolling()
      }
    }
  })
}

// 返回首页
const goBack = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

// 获取状态图标样式类
const getStatusIconClass = (): string => {
  switch (taskStatus.value) {
    case 'processing': return 'status-processing'
    case 'completed': return 'status-success'
    case 'failed': return 'status-error'
    default: return 'status-pending'
  }
}

// 获取步骤数字样式类
const getStepNumberClass = (index: number): string => {
  if (currentStep.value > index + 1) return 'step-completed'
  if (currentStep.value === index + 1 && taskStatus.value === 'processing') return 'step-processing'
  if (currentStep.value >= index + 1) return 'step-active'
  return 'step-pending'
}

// 获取进度百分比
const getProgressPercent = (): number => {
  return Math.round((currentStep.value / steps.value.length) * 100)
}

// 获取预计剩余时间
const getEstimatedTime = (): string => {
  const elapsed = (Date.now() - startTime.value) / 1000 / 60 // 分钟
  const progress = currentStep.value / steps.value.length
  if (progress === 0) return '计算中'

  const totalTime = elapsed / progress
  const remaining = totalTime - elapsed
  return Math.max(1, Math.ceil(remaining)).toString()
}

// 获取单个步骤进度
const getStepProgress = (index: number): number => {
  if (currentStep.value !== index + 1 || taskStatus.value !== 'processing') return 0

  // 模拟步骤内部进度
  const elapsed = (Date.now() - startTime.value) / 1000
  const stepDuration = 30 // 假设每个步骤30秒
  const stepElapsed = elapsed % stepDuration
  return Math.min(90, (stepElapsed / stepDuration) * 100) // 最多显示90%
}
</script>

<style scoped>
.process-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
  padding-bottom: 120rpx; /* 为tabbar留出空间 */
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 状态卡片 */
.status-card {
  margin-bottom: 48rpx;
}

.status-content {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-icon-wrapper {
  margin-right: 32rpx;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-processing {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.status-success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-pending {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.processing-icon {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.status-text {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.status-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 进度条区域 */
.progress-section {
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  border-radius: 20rpx;
  padding: 32rpx;
  border: 2rpx solid #c7d2fe;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.progress-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #4338ca;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: 700;
  color: #4338ca;
}

.progress-bar-wrapper {
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #6366f1;
  text-align: center;
  display: block;
}

/* 步骤卡片 */
.steps-card {
  margin-bottom: 48rpx;
}

.steps-container {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 48rpx;
  position: relative;
}

.step-indicator {
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 700;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.step-pending {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.step-active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.step-processing {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

.step-completed {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.step-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.step-connector {
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 4rpx;
  height: 48rpx;
  background-color: #e5e7eb;
  border-radius: 2rpx;
  z-index: 1;
}

.step-connector.active {
  background: linear-gradient(180deg, #6366f1, #8b5cf6);
}

.step-content {
  flex: 1;
  padding-top: 12rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.step-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.step-time {
  display: block;
  font-size: 22rpx;
  color: #9ca3af;
}

.step-progress {
  margin-top: 16rpx;
}

.step-progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #e5e7eb;
  border-radius: 3rpx;
  overflow: hidden;
}

.step-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 错误卡片 */
.error-card {
  margin-bottom: 48rpx;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 2rpx solid #fecaca;
}

.error-content {
  display: flex;
  align-items: flex-start;
}

.error-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  margin-top: 4rpx;
}

.error-text {
  flex: 1;
}

.error-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 12rpx;
}

.error-message {
  display: block;
  font-size: 26rpx;
  color: #ef4444;
  line-height: 1.5;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}
</style>
