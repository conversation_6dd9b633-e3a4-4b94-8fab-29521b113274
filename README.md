# 视语翻译 - AI智能视频字幕生成小程序

基于uni-app开发的微信小程序，集成腾讯云VOD服务，实现视频自动字幕生成功能。

## 功能特性

- 📹 **视频上传**: 支持多种格式视频文件上传
- 🎤 **语音识别**: 基于腾讯云VOD的AI语音识别技术
- 📝 **字幕生成**: 自动生成SRT格式字幕文件
- 🎬 **视频合成**: 将字幕烧录到原视频中
- 📱 **移动优化**: 专为移动端设计的用户界面
- ☁️ **云端处理**: 基于微信小程序云开发

## 技术架构

### 前端技术栈
- **框架**: uni-app (Vue 3 + TypeScript)
- **构建工具**: Vite
- **UI组件**: 自定义组件库
- **状态管理**: Vue 3 Composition API

### 后端服务
- **云开发**: 微信小程序云开发
- **视频处理**: 腾讯云点播 VOD
- **数据库**: 云数据库 NoSQL
- **存储**: 云存储

### 核心依赖
- `vod-wx-sdk-v2`: 腾讯云VOD上传SDK
- `vue`: Vue 3框架
- `typescript`: TypeScript支持

## 项目结构

```
video-translate-app/
├── src/
│   ├── components/          # 公共组件
│   │   ├── VideoUploader.vue    # 视频上传组件
│   │   └── TaskStatus.vue       # 任务状态组件
│   ├── pages/              # 页面文件
│   │   ├── index/              # 首页
│   │   ├── upload/             # 上传页面
│   │   ├── process/            # 处理进度页面
│   │   ├── result/             # 结果展示页面
│   │   └── history/            # 历史记录页面
│   ├── utils/              # 工具函数
│   │   ├── api.ts              # API调用封装
│   │   └── common.ts           # 通用工具函数
│   ├── static/             # 静态资源
│   ├── App.vue             # 应用入口
│   ├── main.ts             # 主入口文件
│   ├── pages.json          # 页面配置
│   └── manifest.json       # 应用配置
├── .vscode/                # VSCode配置
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── README.md               # 项目说明
```

## 开发环境搭建

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- 微信开发者工具

### 安装依赖
```bash
npm install
```

### 开发调试

#### 1. 微信小程序开发
```bash
# 启动微信小程序开发模式
npm run dev:mp-weixin
```

然后使用微信开发者工具打开 `dist/dev/mp-weixin` 目录。

#### 2. H5开发调试
```bash
# 启动H5开发模式
npm run dev:h5
```

访问 `http://localhost:5173` 进行H5调试。

### 构建发布

#### 微信小程序构建
```bash
npm run build:mp-weixin
```

构建产物在 `dist/build/mp-weixin` 目录。

#### H5构建
```bash
npm run build:h5
```

构建产物在 `dist/build/h5` 目录。

## 配置说明

### 1. 微信小程序配置
在 `src/manifest.json` 中配置小程序AppID：
```json
{
  "mp-weixin": {
    "appid": "你的小程序AppID"
  }
}
```

### 2. 云开发配置
在微信开发者工具中：
1. 开通云开发服务
2. 创建云环境
3. 在代码中初始化云开发

### 3. 腾讯云VOD配置
1. 开通腾讯云VOD服务
2. 获取API密钥
3. 配置任务流模板
4. 设置事件回调

## 云函数部署

项目需要部署以下云函数：

1. **get-upload-signature**: 获取VOD上传签名
2. **create-task**: 创建任务记录
3. **handle-vod-callback**: 处理VOD回调
4. **get-task-status**: 获取任务状态
5. **get-video-url**: 获取视频播放URL
6. **get-history-list**: 获取历史记录

详细的云函数代码请参考需求文档。

## 数据库设计

### tasks 集合
```javascript
{
  _id: String,              // 记录ID
  _openid: String,          // 用户OpenID
  originalVideoFileId: String,  // 原始视频FileId
  processedVideoFileId: String, // 处理后视频FileId
  status: String,           // 状态: processing/completed/failed
  errorMsg: String,         // 错误信息
  createTime: Date,         // 创建时间
  finishTime: Date,         // 完成时间
  fileName: String,         // 文件名
  fileSize: Number,         // 文件大小
  duration: Number          // 视频时长
}
```

## 开发指南

### 添加新页面
1. 在 `src/pages/` 下创建页面目录
2. 在 `src/pages.json` 中注册页面路由
3. 编写页面组件

### 添加新组件
1. 在 `src/components/` 下创建组件文件
2. 使用 TypeScript 编写组件逻辑
3. 在需要的页面中引入使用

### API调用
使用封装的API工具函数：
```typescript
import { getUploadSignature, createTask } from '@/utils/api'

// 获取上传签名
const signature = await getUploadSignature()

// 创建任务
const task = await createTask(fileId)
```

## 注意事项

1. **文件大小限制**: 视频文件最大100MB
2. **时长限制**: 视频时长最大5分钟
3. **格式支持**: 支持MP4、MOV等常见格式
4. **网络要求**: 需要稳定的网络连接进行上传和处理

## 故障排除

### 常见问题

1. **上传失败**
   - 检查网络连接
   - 确认文件大小和格式
   - 查看控制台错误信息

2. **处理超时**
   - 视频处理通常需要1-3分钟
   - 可以刷新页面查看最新状态

3. **云函数调用失败**
   - 检查云开发环境配置
   - 确认云函数部署状态

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
