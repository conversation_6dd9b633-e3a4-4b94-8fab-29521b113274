<template>
  <view class="task-status card">
    <view class="card-body">
      <!-- 状态图标和文本 -->
      <view class="status-header">
        <view class="status-icon-wrapper" :class="getStatusIconClass()">
          <view class="status-icon">
            <text v-if="status === 'processing'" class="icon processing">⚡</text>
            <text v-else-if="status === 'completed'" class="icon success">✅</text>
            <text v-else-if="status === 'failed'" class="icon error">❌</text>
            <text v-else class="icon pending">⏸️</text>
          </view>
        </view>

        <view class="status-text">
          <text class="status-title">{{ statusTitle }}</text>
          <text class="status-desc">{{ statusDesc }}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view v-if="showProgress" class="progress-section">
        <view class="progress-header">
          <text class="progress-label">处理进度</text>
          <text class="progress-percent">{{ progressPercent }}%</text>
        </view>
        <view class="progress-bar-wrapper">
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: progressPercent + '%' }"
            ></view>
          </view>
        </view>
        <text class="progress-text">{{ progressText }}</text>
      </view>

      <!-- 处理步骤 -->
      <view v-if="showSteps" class="steps-section">
        <view class="steps-header">
          <text class="steps-title">处理步骤</text>
        </view>
        <view class="steps-container">
          <view
            v-for="(step, index) in steps"
            :key="index"
            class="step-item"
            :class="{
              active: currentStep >= index + 1,
              completed: currentStep > index + 1,
              processing: currentStep === index + 1 && status === 'processing'
            }"
          >
            <view class="step-indicator">
              <view class="step-number" :class="getStepNumberClass(index)">
                <text v-if="currentStep > index + 1">✓</text>
                <text v-else-if="currentStep === index + 1 && status === 'processing'" class="step-loading">⟳</text>
                <text v-else>{{ index + 1 }}</text>
              </view>
              <view v-if="index < steps.length - 1" class="step-connector" :class="{ active: currentStep > index + 1 }"></view>
            </view>

            <view class="step-content">
              <text class="step-title">{{ step.title }}</text>
              <text class="step-desc">{{ step.desc }}</text>
              <text v-if="step.time" class="step-time">{{ step.time }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 错误信息 -->
      <view v-if="errorMsg" class="error-section">
        <view class="error-content">
          <text class="error-icon">⚠️</text>
          <view class="error-text">
            <text class="error-title">处理失败</text>
            <text class="error-message">{{ errorMsg }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view v-if="showActions" class="actions-section">
        <slot name="actions">
          <button
            v-if="status === 'completed'"
            @click="$emit('view-result')"
            class="btn btn-primary btn-lg"
          >
            <text class="btn-icon">👀</text>
            <text>查看结果</text>
          </button>

          <button
            v-if="status === 'failed'"
            @click="$emit('retry')"
            class="btn btn-primary btn-lg"
          >
            <text class="btn-icon">🔄</text>
            <text>重新处理</text>
          </button>

          <button
            v-if="status === 'processing'"
            @click="$emit('cancel')"
            class="btn btn-secondary"
          >
            <text class="btn-icon">⏹️</text>
            <text>取消处理</text>
          </button>
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getStatusText, formatTime } from '@/utils/common'

// Props
interface Step {
  title: string
  desc: string
  time?: string
}

interface Props {
  status: 'pending' | 'processing' | 'completed' | 'failed'
  currentStep?: number
  progress?: number
  errorMsg?: string
  showProgress?: boolean
  showSteps?: boolean
  showActions?: boolean
  steps?: Step[]
  customTitle?: string
  customDesc?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentStep: 0,
  progress: 0,
  showProgress: false,
  showSteps: true,
  showActions: true,
  steps: () => [
    { title: '视频上传', desc: '视频文件上传到云端' },
    { title: '语音识别', desc: 'AI识别视频中的语音内容' },
    { title: '字幕生成', desc: '生成SRT字幕文件' },
    { title: '视频合成', desc: '将字幕烧录到视频中' }
  ]
})

// Emits
interface Emits {
  (e: 'view-result'): void
  (e: 'retry'): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 计算属性
const statusTitle = computed(() => {
  if (props.customTitle) return props.customTitle
  
  const titleMap: Record<string, string> = {
    'pending': '等待处理',
    'processing': '正在处理中...',
    'completed': '处理完成',
    'failed': '处理失败'
  }
  
  return titleMap[props.status] || '未知状态'
})

const statusDesc = computed(() => {
  if (props.customDesc) return props.customDesc
  
  const descMap: Record<string, string> = {
    'pending': '任务已创建，等待开始处理',
    'processing': '请耐心等待，通常需要1-3分钟',
    'completed': '视频字幕已生成完成',
    'failed': '处理过程中出现错误，请重试'
  }
  
  return descMap[props.status] || ''
})

const progressPercent = computed(() => {
  if (props.progress > 0) return props.progress
  
  // 根据当前步骤计算进度
  if (props.currentStep > 0 && props.steps.length > 0) {
    return Math.round((props.currentStep / props.steps.length) * 100)
  }
  
  return 0
})

const progressText = computed(() => {
  if (props.status === 'processing') {
    return `预计还需 ${getEstimatedTime()} 分钟`
  }
  return ''
})

// 获取状态图标样式类
const getStatusIconClass = (): string => {
  switch (props.status) {
    case 'processing': return 'status-processing'
    case 'completed': return 'status-success'
    case 'failed': return 'status-error'
    default: return 'status-pending'
  }
}

// 获取步骤数字样式类
const getStepNumberClass = (index: number): string => {
  if (props.currentStep > index + 1) return 'step-completed'
  if (props.currentStep === index + 1 && props.status === 'processing') return 'step-processing'
  if (props.currentStep >= index + 1) return 'step-active'
  return 'step-pending'
}

// 获取预计剩余时间
const getEstimatedTime = (): string => {
  const progress = progressPercent.value
  if (progress === 0) return '计算中'

  // 简单的时间估算逻辑
  const remaining = (100 - progress) / progress
  return Math.max(1, Math.ceil(remaining)).toString()
}
</script>

<style scoped>
.task-status {
  margin-bottom: 32rpx;
}

/* 状态头部 */
.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-processing {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.status-success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-pending {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 60rpx;
  display: block;
}

.icon.processing {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.status-text {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.status-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 进度条区域 */
.progress-section {
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  border-radius: 20rpx;
  padding: 32rpx;
  border: 2rpx solid #c7d2fe;
  margin-bottom: 32rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.progress-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #4338ca;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: 700;
  color: #4338ca;
}

.progress-bar-wrapper {
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #6366f1;
  text-align: center;
  display: block;
}

/* 步骤区域 */
.steps-section {
  margin-bottom: 32rpx;
}

.steps-header {
  margin-bottom: 32rpx;
}

.steps-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.steps-container {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 48rpx;
  position: relative;

}

.step-item:last-child {
  margin-bottom: 0;
}

.step-indicator {
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 700;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.step-pending {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.step-active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.step-processing {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

.step-completed {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.step-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.step-connector {
  position: absolute;
  top: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 4rpx;
  height: 48rpx;
  background-color: #e5e7eb;
  border-radius: 2rpx;
  z-index: 1;

}

.step-connector.active {
  background: linear-gradient(180deg, #6366f1, #8b5cf6);
}

.step-content {
  flex: 1;
  padding-top: 12rpx;
}

.step-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.step-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.step-time {
  display: block;
  font-size: 22rpx;
  color: #9ca3af;
}

/* 错误区域 */
.error-section {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 2rpx solid #fecaca;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.error-content {
  display: flex;
  align-items: flex-start;
}

.error-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  margin-top: 4rpx;
}

.error-text {
  flex: 1;
}

.error-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 12rpx;
}

.error-message {
  display: block;
  font-size: 26rpx;
  color: #ef4444;
  line-height: 1.5;
}

/* 操作按钮 */
.actions-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}
</style>
