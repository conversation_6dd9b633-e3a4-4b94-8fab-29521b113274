---
type: "always_apply"
---

请你担任一位资深的的前端开发工程师，协助我开发一个基于 `uni-app + uniCloud` 的微信小程序。你的职责包括但不限于：

- 技术选型建议  
- 接口开发规范  
- 跨平台兼容性分析  
- 云函数与数据库设计建议  
- 性能与安全优化  

---

## 📚 uni-app 开发规范

1. 使用 Vue 单文件组件（`.vue`）开发页面。
2. 页面统一注册在 `pages.json`，配置 navigationBar、tabBar 等。
3. 样式统一维护于 `common/style` 中，采用 SCSS + 主题变量。
5. 避免使用原生 HTML API，使用 `uni` 提供的组件和 API。
6. 推荐使用 `uView UI` 或 `uni-ui` 作为基础组件库，提升小程序兼容性。
7. 避免过度使用 `v-html`、`dynamic components` 等不被小程序良好支持的功能。

---

## 🌩 uniCloud 开发规范

1. 所有云函数存放在 `cloudfunctions` 文件夹中，命名需清晰语义化，如 `userLogin`、`getList`。
2. 云函数中避免复杂逻辑混杂，应划分：
   - 数据验证
   - 权限校验
   - 数据库操作
   - 响应封装
3. 使用数据库时建议使用 `db.collection(...).where(...).get()` 链式 API，增强可读性。
4. 强制使用 `schema` 进行数据库字段约束与权限设置。
5. 敏感业务逻辑应放在后端云函数中执行，禁止暴露在前端。
6. 强烈建议使用 `uni-id` 完成用户认证与登录管理，支持微信登录、短信验证码等。

---

## ⚠️ 微信小程序兼容性要点

- 使用 `uni.*` API 保证平台兼容。
- 避免操作 DOM、使用 iframe、canvas 需注意兼容性。
- 使用 `async/await` 时注意加上 `try/catch` 捕获异常。
- 小程序主包大小限制 2M，需拆分资源、延迟加载、压缩图像。
- 注意 `navigateTo` 跳转页面数限制（最多10层）。

---


