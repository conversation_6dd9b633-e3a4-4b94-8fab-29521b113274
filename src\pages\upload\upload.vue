<template>
  <view class="upload-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">视频上传</text>
      <text class="page-subtitle">选择您的视频文件开始制作字幕</text>
    </view>

    <!-- 上传区域 -->
    <view class="upload-section">
      <view
        class="upload-area"
        :class="{ 'upload-area-active': isDragOver, 'upload-area-disabled': uploading }"
        @click="chooseVideo"
      >
        <view class="upload-content">
          <view class="upload-icon-wrapper">
            <text class="upload-icon">📹</text>
          </view>
          <view class="upload-text">
            <text class="upload-main-text">点击选择视频文件</text>
            <text class="upload-sub-text">或拖拽文件到此区域</text>
            <text class="upload-tips">支持 MP4、MOV、AVI 等格式，最大 100MB</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 视频预览卡片 -->
    <view v-if="videoInfo && !uploading" class="video-preview-card card">
      <view class="card-header">
        <text class="card-title">视频预览</text>
        <button @click="resetVideo" class="btn btn-ghost btn-sm">重新选择</button>
      </view>

      <view class="card-body">
        <video
          :src="videoInfo.tempFilePath"
          controls
          class="preview-video"
          :poster="videoInfo.thumbTempFilePath"
          show-center-play-btn
          show-fullscreen-btn
        ></video>

        <view class="video-info-grid">
          <view class="info-item">
            <text class="info-label">文件大小</text>
            <text class="info-value">{{ formatFileSize(videoInfo.size) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">视频时长</text>
            <text class="info-value">{{ formatDuration(videoInfo.duration) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">文件格式</text>
            <text class="info-value">{{ getFileExtension(videoInfo.tempFilePath) }}</text>
          </view>
        </view>
      </view>

      <view class="card-footer">
        <button @click="startUpload" class="btn btn-primary btn-lg upload-btn">
          <text class="btn-icon">🚀</text>
          <text>开始上传</text>
        </button>
      </view>
    </view>

    <!-- 上传进度卡片 -->
    <view v-if="uploading" class="upload-progress-card card">
      <view class="card-body">
        <view class="progress-header">
          <text class="progress-title">正在上传视频...</text>
          <text class="progress-percent">{{ uploadPercent }}%</text>
        </view>

        <view class="progress-bar-wrapper">
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: uploadPercent + '%' }"
            ></view>
          </view>
        </view>

        <view class="progress-info">
          <text class="progress-text">请保持网络连接，预计还需 {{ estimatedTime }} 分钟</text>
        </view>
      </view>
    </view>

    <!-- 上传提示 -->
    <view class="upload-tips-section">
      <view class="tips-card card">
        <view class="card-body">
          <text class="tips-title">上传提示</text>
          <view class="tips-list">
            <view class="tip-item">
              <text class="tip-icon">✅</text>
              <text class="tip-text">支持 MP4、MOV、AVI、WMV 等主流格式</text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">📏</text>
              <text class="tip-text">文件大小不超过 100MB，时长不超过 5 分钟</text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">🎯</text>
              <text class="tip-text">建议上传清晰度较高的视频以获得更好效果</text>
            </view>
            <view class="tip-item">
              <text class="tip-icon">⚡</text>
              <text class="tip-text">处理时间通常为视频时长的 1-2 倍</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VodUploader from 'vod-wx-sdk-v2'
import { formatFileSize, formatDuration } from '@/utils/common'
import { getUploadSignature as getSignature, createTask as createTaskRecord } from '@/utils/api'

// 响应式数据
const videoInfo = ref<any>(null)
const uploading = ref(false)
const uploadPercent = ref(0)
const isDragOver = ref(false)

// 计算预计剩余时间
const estimatedTime = computed(() => {
  if (uploadPercent.value === 0) return '计算中'
  const remaining = (100 - uploadPercent.value) / uploadPercent.value
  return Math.max(1, Math.ceil(remaining)).toString()
})

// 选择视频
const chooseVideo = () => {
  if (uploading.value) return

  uni.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: 300, // 最大5分钟
    success: (res) => {
      console.log('选择视频成功:', res)

      // 验证文件
      if (res.size > 100 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过100MB',
          icon: 'none'
        })
        return
      }

      if (res.duration > 300) {
        uni.showToast({
          title: '视频时长不能超过5分钟',
          icon: 'none'
        })
        return
      }

      videoInfo.value = res
    },
    fail: (err) => {
      console.error('选择视频失败:', err)
      uni.showToast({
        title: '选择视频失败',
        icon: 'none'
      })
    }
  })
}

// 开始上传
const startUpload = async () => {
  if (!videoInfo.value) return
  
  try {
    uploading.value = true
    uploadPercent.value = 0
    
    // 获取上传签名
    const signature = await getUploadSignature()
    
    // 使用VOD SDK上传
    const uploader = VodUploader.start({
      mediaFile: videoInfo.value,
      getSignature: () => signature,
      procedure: 'SubtitleProcessing', // 任务流名称
      onProgress: (progress: any) => {
        uploadPercent.value = Math.round(progress.percent * 100)
      },
      onFinish: async (result: any) => {
        console.log('上传完成:', result)
        
        // 创建任务记录
        await createTask(result.fileId)
        
        // 跳转到处理页面
        uni.navigateTo({
          url: `/pages/process/process?fileId=${result.fileId}`
        })
      },
      onError: (error: any) => {
        console.error('上传失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
        uploading.value = false
      }
    })
  } catch (error) {
    console.error('上传过程出错:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
    uploading.value = false
  }
}

// 获取上传签名
const getUploadSignature = async (): Promise<string> => {
  return await getSignature()
}

// 创建任务记录
const createTask = async (fileId: string) => {
  return await createTaskRecord(fileId)
}

// 重置视频
const resetVideo = () => {
  videoInfo.value = null
  uploading.value = false
  uploadPercent.value = 0
}

// 获取文件扩展名
const getFileExtension = (filePath: string): string => {
  if (!filePath) return 'Unknown'
  const extension = filePath.split('.').pop()?.toUpperCase()
  return extension || 'Unknown'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}

// 格式化时长
const formatDuration = (duration: number): string => {
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.upload-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
  padding-bottom: 120rpx; /* 为tabbar留出空间 */
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: white;
  border: 3rpx dashed #d1d5db;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:active {
  border-color: #6366f1;
  background-color: #f8faff;
}

.upload-area-active {
  border-color: #6366f1;
  background-color: #f0f4ff;
  transform: scale(1.02);
}

.upload-area-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-area-disabled:active {
  border-color: #d1d5db;
  background-color: white;
  transform: none;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.2);
}

.upload-icon {
  font-size: 60rpx;
}

.upload-text {
  text-align: center;
}

.upload-main-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.upload-sub-text {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
}

.upload-tips {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.5;
}

/* 视频预览卡片 */
.video-preview-card {
  margin-bottom: 48rpx;
}

.preview-video {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  background-color: #f3f4f6;
}

.video-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  margin-top: 32rpx;
}

.info-item {
  text-align: center;
}

.info-label {
  display: block;
  font-size: 24rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
}

.info-value {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.upload-btn {
  width: 100%;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.2);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 上传进度卡片 */
.upload-progress-card {
  margin-bottom: 48rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: 700;
  color: #6366f1;
}

.progress-bar-wrapper {
  margin-bottom: 24rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-info {
  text-align: center;
}

.progress-text {
  font-size: 26rpx;
  color: #6b7280;
}

/* 上传提示区域 */
.upload-tips-section {
  margin-bottom: 48rpx;
}

.tips-card {
  background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
  border: 2rpx solid #c7d2fe;
}

.tips-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.5;
  flex: 1;
}
</style>
