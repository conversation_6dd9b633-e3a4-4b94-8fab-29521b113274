// uniCloud云函数：获取最近任务
'use strict';

/**
 * 获取用户最近的任务记录
 */
exports.main = async (event, context) => {
  try {
    const { limit = 3, deviceId } = event
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    // 构建查询条件
    let whereCondition = {}
    if (deviceId) {
      whereCondition.deviceId = deviceId
    }
    
    // 查询最近任务
    const result = await db.collection('tasks')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get()
    
    return {
      errCode: 0,
      errMsg: '查询成功',
      data: result.data
    }
    
  } catch (error) {
    console.error('查询最近任务失败:', error)
    return {
      errCode: 'QUERY_RECENT_TASKS_FAILED',
      errMsg: '查询最近任务失败: ' + error.message
    }
  }
}
