<template>
  <view class="ui-test-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">UI组件测试</text>
      <text class="page-subtitle">现代化设计系统展示</text>
    </view>

    <!-- 颜色系统测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">颜色系统</text>
      </view>
      <view class="card-body">
        <view class="color-grid">
          <view class="color-item">
            <view class="color-swatch bg-primary"></view>
            <text class="color-name">Primary</text>
          </view>
          <view class="color-item">
            <view class="color-swatch" style="background-color: #10b981;"></view>
            <text class="color-name">Success</text>
          </view>
          <view class="color-item">
            <view class="color-swatch" style="background-color: #f59e0b;"></view>
            <text class="color-name">Warning</text>
          </view>
          <view class="color-item">
            <view class="color-swatch" style="background-color: #ef4444;"></view>
            <text class="color-name">Error</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 按钮组件测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">按钮组件</text>
      </view>
      <view class="card-body">
        <view class="button-grid">
          <button class="btn btn-primary">Primary</button>
          <button class="btn btn-secondary">Secondary</button>
          <button class="btn btn-outline">Outline</button>
          <button class="btn btn-ghost">Ghost</button>
          <button class="btn btn-primary btn-sm">Small</button>
          <button class="btn btn-primary btn-lg">Large</button>
        </view>
      </view>
    </view>

    <!-- 标签组件测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">标签组件</text>
      </view>
      <view class="card-body">
        <view class="tag-grid">
          <view class="tag tag-primary">Primary</view>
          <view class="tag tag-success">Success</view>
          <view class="tag tag-warning">Warning</view>
          <view class="tag tag-error">Error</view>
        </view>
      </view>
    </view>

    <!-- 卡片组件测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">卡片组件</text>
      </view>
      <view class="card-body">
        <view class="card hover-lift">
          <view class="card-header">
            <text class="card-title">示例卡片</text>
          </view>
          <view class="card-body">
            <text>这是一个现代化设计的卡片组件，具有悬停效果和阴影。</text>
          </view>
          <view class="card-footer">
            <button class="btn btn-primary btn-sm">操作</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 输入框组件测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">输入框组件</text>
      </view>
      <view class="card-body">
        <view class="input-grid">
          <input class="input" placeholder="请输入内容" />
          <input class="input" placeholder="禁用状态" disabled />
        </view>
      </view>
    </view>

    <!-- 网格系统测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">网格系统</text>
      </view>
      <view class="card-body">
        <view class="grid-responsive grid-4">
          <view class="grid-item">1</view>
          <view class="grid-item">2</view>
          <view class="grid-item">3</view>
          <view class="grid-item">4</view>
        </view>
      </view>
    </view>

    <!-- 动画效果测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">动画效果</text>
      </view>
      <view class="card-body">
        <view class="animation-grid">
          <view class="animation-item fade-in">淡入动画</view>
          <view class="animation-item slide-in-up">滑入动画</view>
          <view class="animation-item scale-in">缩放动画</view>
          <view class="animation-item loading-pulse">脉冲动画</view>
        </view>
      </view>
    </view>

    <!-- 响应式测试 -->
    <view class="test-section card">
      <view class="card-header">
        <text class="card-title">响应式布局</text>
      </view>
      <view class="card-body">
        <view class="responsive-demo">
          <view class="responsive-item show-sm">仅在小屏显示</view>
          <view class="responsive-item show-md">仅在中屏显示</view>
          <view class="responsive-item hidden-sm">小屏隐藏</view>
        </view>
      </view>
    </view>

    <!-- 测试操作 -->
    <view class="test-actions">
      <button @click="runTests" class="btn btn-primary btn-lg">
        <text class="btn-icon">🧪</text>
        <text>运行测试</text>
      </button>
      <button @click="resetTests" class="btn btn-secondary">
        <text class="btn-icon">🔄</text>
        <text>重置</text>
      </button>
    </view>

    <!-- 测试结果 -->
    <view v-if="testResults.length > 0" class="test-results card">
      <view class="card-header">
        <text class="card-title">测试结果</text>
      </view>
      <view class="card-body">
        <view v-for="(result, index) in testResults" :key="index" class="test-result-item">
          <text class="result-icon">{{ result.passed ? '✅' : '❌' }}</text>
          <text class="result-text">{{ result.name }}</text>
          <text class="result-status">{{ result.passed ? '通过' : '失败' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 测试结果
const testResults = ref<Array<{name: string, passed: boolean}>>([])

// 运行测试
const runTests = () => {
  testResults.value = [
    { name: '颜色系统', passed: true },
    { name: '按钮组件', passed: true },
    { name: '卡片组件', passed: true },
    { name: '输入框组件', passed: true },
    { name: '网格系统', passed: true },
    { name: '动画效果', passed: true },
    { name: '响应式布局', passed: true }
  ]
  
  uni.showToast({
    title: '测试完成',
    icon: 'success'
  })
}

// 重置测试
const resetTests = () => {
  testResults.value = []
  
  uni.showToast({
    title: '已重置',
    icon: 'none'
  })
}
</script>

<style scoped>
.ui-test-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 测试区域 */
.test-section {
  margin-bottom: 32rpx;
}

/* 颜色网格 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.color-item {
  text-align: center;
}

.color-swatch {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.color-name {
  font-size: 24rpx;
  color: #6b7280;
}

/* 按钮网格 */
.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

/* 标签网格 */
.tag-grid {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

/* 输入框网格 */
.input-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 网格项 */
.grid-item {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 32rpx;
  border-radius: 12rpx;
  text-align: center;
  font-weight: 600;
}

/* 动画网格 */
.animation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.animation-item {
  background: #f3f4f6;
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
  font-size: 26rpx;
  color: #1f2937;
}

/* 响应式演示 */
.responsive-demo {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.responsive-item {
  background: #e0e7ff;
  color: #4338ca;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

/* 测试操作 */
.test-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.test-actions .btn:first-child {
  flex: 2;
}

.test-actions .btn:last-child {
  flex: 1;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

/* 测试结果 */
.test-results {
  margin-bottom: 32rpx;
}

.test-result-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.test-result-item:last-child {
  border-bottom: none;
}
}

.result-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.result-text {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
}

.result-status {
  font-size: 24rpx;
  color: #6b7280;
}
</style>
